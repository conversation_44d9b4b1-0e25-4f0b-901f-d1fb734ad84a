<?php

namespace App\Controllers;

use CodeIgniter\Controller;

class AdminController extends BaseController
{
    protected $data = [];
    
    public function __construct()
    {
        // Initialize common admin data
        $this->data['user_name'] = session('user_name') ?? '<PERSON><PERSON>';
        $this->data['user_email'] = session('user_email') ?? '<EMAIL>';
        $this->data['user_role'] = session('user_role') ?? 'administrator';
        
        // Sample notification data
        $this->data['notification_count'] = 3;
        $this->data['notifications'] = [
            [
                'title' => 'New order received',
                'time' => '2 minutes ago',
                'url' => base_url('admin/orders/247')
            ],
            [
                'title' => 'Low stock alert',
                'time' => '15 minutes ago',
                'url' => base_url('admin/inventory')
            ],
            [
                'title' => 'New user registration',
                'time' => '1 hour ago',
                'url' => base_url('admin/users')
            ]
        ];
        
        // Sample pending orders count
        $this->data['pending_orders'] = 5;
    }
    
    public function dashboard()
    {
        $this->data['title'] = 'Admin Dashboard - DCBuyer';
        $this->data['description'] = 'DCBuyer Administration Panel Dashboard';
        $this->data['active_menu'] = 'dashboard';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')]
        ];
        
        // Page header
        $this->data['page_title'] = 'Dashboard';
        $this->data['page_description'] = 'Welcome to the DCBuyer administration panel. Monitor your platform performance and manage operations.';
        $this->data['page_actions'] = '
            <button class="btn btn-admin-primary" onclick="window.location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        ';
        
        return view('admin/admin_dashboard', $this->data);
    }
    
    public function products()
    {
        $this->data['title'] = 'Products Management - DCBuyer Admin';
        $this->data['active_menu'] = 'products';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Products', 'url' => base_url('admin/products')]
        ];
        
        $this->data['page_title'] = 'Products Management';
        $this->data['page_description'] = 'Manage all agricultural products listed on your platform.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/products/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Product
            </a>
        ';
        
        // You would load actual product data here
        $this->data['products'] = [
            [
                'id' => 1,
                'name' => 'Premium Rice',
                'category' => 'Grains',
                'farmer' => 'John Farmer',
                'price' => '$4.50/kg',
                'stock' => '500kg',
                'status' => 'Active'
            ],
            [
                'id' => 2,
                'name' => 'Organic Wheat',
                'category' => 'Grains',
                'farmer' => 'Mike Smith',
                'price' => '$3.20/kg',
                'stock' => '300kg',
                'status' => 'Active'
            ],
            // Add more sample products as needed
        ];
        
        return view('admin/products_list', $this->data);
    }
    
    public function users()
    {
        $this->data['title'] = 'Users Management - DCBuyer Admin';
        $this->data['active_menu'] = 'users';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Users', 'url' => base_url('admin/users')]
        ];
        
        $this->data['page_title'] = 'Users Management';
        $this->data['page_description'] = 'Manage platform users including farmers, buyers, and administrators.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/users/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-user-plus me-2"></i>Add New User
            </a>
        ';
        
        return view('admin/users_list', $this->data);
    }
    
    public function orders()
    {
        $this->data['title'] = 'Orders Management - DCBuyer Admin';
        $this->data['active_menu'] = 'orders';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Orders', 'url' => base_url('admin/orders')]
        ];
        
        $this->data['page_title'] = 'Orders Management';
        $this->data['page_description'] = 'Monitor and manage all commodity orders on your platform.';
        
        return view('admin/orders_list', $this->data);
    }
    
    public function analytics()
    {
        $this->data['title'] = 'Analytics - DCBuyer Admin';
        $this->data['active_menu'] = 'analytics';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Analytics', 'url' => base_url('admin/analytics')]
        ];
        
        $this->data['page_title'] = 'Analytics & Reports';
        $this->data['page_description'] = 'View detailed analytics and generate reports for your platform.';
        
        return view('admin/analytics', $this->data);
    }
    
    public function settings()
    {
        $this->data['title'] = 'Settings - DCBuyer Admin';
        $this->data['active_menu'] = 'settings';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Settings', 'url' => base_url('admin/settings')]
        ];
        
        $this->data['page_title'] = 'Platform Settings';
        $this->data['page_description'] = 'Configure your DCBuyer platform settings and preferences.';
        
        return view('admin/settings', $this->data);
    }
}